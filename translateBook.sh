#!/bin/bash

# translateBook.sh - Complete book translation workflow
# Automatically starts LLM server, translates the full book, and stops server

# Configuration
INPUT_FILE="test_small.txt"
STYLE_FILE="style.txt"
OUTPUT_FILE="translated_book.txt"
SAMPLE_SOURCE_FILE="sample_source.txt"
SAMPLE_TARGET_FILE="sample_target.txt"
MODEL_NAME="gemma-2-27b-it"
CHUNK_TOKENS=100
WORKERS=4
TEMPERATURE=0.3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

# Function to check if server is running
check_server() {
    curl -s http://localhost:8080/v1/models >/dev/null 2>&1
    return $?
}

# Function to wait for server to be ready
wait_for_server() {
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for llama.cpp server to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if check_server; then
            print_success "Server is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "Server failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to estimate translation time
estimate_time() {
    local file_size=$(wc -l < "$INPUT_FILE" 2>/dev/null || echo "0")
    local estimated_chunks=$((file_size / 50))  # Rough estimate: 50 lines per chunk
    local time_per_chunk=25  # seconds
    local total_seconds=$((estimated_chunks * time_per_chunk / WORKERS))
    local hours=$((total_seconds / 3600))
    local minutes=$(((total_seconds % 3600) / 60))
    
    if [ $hours -gt 0 ]; then
        echo "${hours}h ${minutes}m"
    else
        echo "${minutes}m"
    fi
}

# Function to cleanup on exit
cleanup() {
    print_status "Cleaning up..."
    ./stopLLM.sh >/dev/null 2>&1
    exit $1
}

# Set up signal handlers
trap 'cleanup 1' INT TERM

# Main script starts here
print_header "📚 BOOK TRANSLATION WORKFLOW"
print_header "=============================="

# Validate input files
print_status "Validating input files..."

if [ ! -f "$INPUT_FILE" ]; then
    print_error "Input file '$INPUT_FILE' not found!"
    exit 1
fi

if [ ! -f "$STYLE_FILE" ]; then
    print_error "Style file '$STYLE_FILE' not found!"
    exit 1
fi

if [ ! -f "translate3_llamacpp.py" ]; then
    print_error "Translation script 'translate3_llamacpp.py' not found!"
    exit 1
fi

# Check for sample files (optional)
SAMPLE_ARGS=""
if [ -f "$SAMPLE_SOURCE_FILE" ] && [ -f "$SAMPLE_TARGET_FILE" ]; then
    print_success "Sample files found - will use for consistency"
    SAMPLE_ARGS="--sample_source $SAMPLE_SOURCE_FILE --sample_target $SAMPLE_TARGET_FILE"
else
    print_warning "Sample files not found - translation will proceed without examples"
fi

# Display configuration
print_header "\n📋 TRANSLATION CONFIGURATION"
echo "Input file:      $INPUT_FILE ($(wc -l < "$INPUT_FILE") lines)"
echo "Style file:      $STYLE_FILE"
echo "Output file:     $OUTPUT_FILE"
echo "Model:           $MODEL_NAME"
echo "Chunk tokens:    $CHUNK_TOKENS"
echo "Workers:         $WORKERS"
echo "Temperature:     $TEMPERATURE"
echo "Estimated time:  $(estimate_time)"

# Ask for confirmation
echo ""
read -p "Proceed with translation? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Translation cancelled by user"
    exit 0
fi

# Check if server is already running
print_status "Checking llama.cpp server status..."
SERVER_WAS_RUNNING=false

if check_server; then
    print_success "Server is already running"
    SERVER_WAS_RUNNING=true
else
    print_status "Starting llama.cpp server..."
    
    # Check if startLLM.sh exists
    if [ ! -f "startLLM.sh" ]; then
        print_error "startLLM.sh script not found!"
        print_error "Please ensure startLLM.sh is in the current directory"
        exit 1
    fi
    
    # Start server in background
    ./startLLM.sh > llama_server.log 2>&1 &
    SERVER_PID=$!
    
    # Wait for server to be ready
    if ! wait_for_server; then
        print_error "Failed to start llama.cpp server"
        print_error "Check llama_server.log for details"
        exit 1
    fi
fi

# Record start time
START_TIME=$(date +%s)
print_header "\n🚀 STARTING TRANSLATION"
print_status "Translation started at $(date '+%Y-%m-%d %H:%M:%S')"

# Run the translation
print_status "Executing translation command..."

TRANSLATION_CMD="python3 translate3_llamacpp.py \
    --txt \"$INPUT_FILE\" \
    --style \"$STYLE_FILE\" \
    --output_txt \"$OUTPUT_FILE\" \
    $SAMPLE_ARGS \
    --model \"$MODEL_NAME\" \
    --chunk_tokens $CHUNK_TOKENS \
    --max_response_tokens $CHUNK_TOKENS \
    --workers $WORKERS \
    --temperature $TEMPERATURE"

echo "Command: $TRANSLATION_CMD"
echo ""

# Execute translation
if eval $TRANSLATION_CMD; then
    # Calculate elapsed time
    END_TIME=$(date +%s)
    ELAPSED=$((END_TIME - START_TIME))
    HOURS=$((ELAPSED / 3600))
    MINUTES=$(((ELAPSED % 3600) / 60))
    SECONDS=$((ELAPSED % 60))
    
    print_header "\n🎉 TRANSLATION COMPLETED SUCCESSFULLY!"
    print_success "Translation finished at $(date '+%Y-%m-%d %H:%M:%S')"
    
    if [ $HOURS -gt 0 ]; then
        print_success "Total time: ${HOURS}h ${MINUTES}m ${SECONDS}s"
    else
        print_success "Total time: ${MINUTES}m ${SECONDS}s"
    fi
    
    # Display file information
    if [ -f "$OUTPUT_FILE" ]; then
        OUTPUT_LINES=$(wc -l < "$OUTPUT_FILE")
        OUTPUT_SIZE=$(du -h "$OUTPUT_FILE" | cut -f1)
        print_success "Output file: $OUTPUT_FILE ($OUTPUT_LINES lines, $OUTPUT_SIZE)"
    fi
    
    # Check for log directory
    if [ -d "logs_llamacpp" ]; then
        LOG_COUNT=$(find logs_llamacpp -name "*.json" | wc -l)
        print_success "Debug logs: logs_llamacpp/ ($LOG_COUNT files)"
    fi
    
    TRANSLATION_SUCCESS=true
else
    print_error "Translation failed!"
    print_error "Check the output above for error details"
    TRANSLATION_SUCCESS=false
fi

# Stop server if we started it
if [ "$SERVER_WAS_RUNNING" = false ]; then
    print_status "Stopping llama.cpp server..."
    ./stopLLM.sh
    
    if [ -f "llama_server.log" ]; then
        print_status "Server logs saved to: llama_server.log"
    fi
else
    print_status "Leaving server running (was already running when script started)"
fi

# Final status
print_header "\n📊 SUMMARY"
if [ "$TRANSLATION_SUCCESS" = true ]; then
    print_success "Book translation completed successfully!"
    print_success "Translated file: $OUTPUT_FILE"
    
    if [ -f "$OUTPUT_FILE" ]; then
        echo ""
        print_status "You can now:"
        echo "  • Review the translation: less \"$OUTPUT_FILE\""
        echo "  • Check specific sections: grep -n \"BLOCK\" \"$OUTPUT_FILE\""
        echo "  • Improve samples: python3 improve_samples.py"
    fi
    
    exit 0
else
    print_error "Translation failed - please check the errors above"
    exit 1
fi
